import { Radio } from "antd";
import React, { useContext, useEffect, useState } from "react";

import { useCommandRunner } from "@/command";
import { useActiveAgentCode, useActiveConversationId } from "@/core";
import { MessageRenderContext } from "@/message";
import { BuildInCommand } from "@/types";

interface RadioGroupInteractiveWidgetProps {
  options: { label: string; value: string }[];
  disabledAfterSended?: boolean;
}

const RadioGroupInteractiveWidget: React.FC<RadioGroupInteractiveWidgetProps> = (props) => {
  const { options, disabledAfterSended = true } = props;
  const [value, setValue] = useState<unknown>();
  const runner = useCommandRunner();
  const [activeConversationId] = useActiveConversationId();
  const [activeAgentCode] = useActiveAgentCode();
  const [disabled, setDisabled] = useState(false);
  const messageRenderContext = useContext(MessageRenderContext);

  const isFreshMessage = messageRenderContext?.message.isFresh;

  const onChange = (value: string) => {
    setValue(value);
    const message = `${value}`;
    runner(BuildInCommand.SendMessage, { message, conversationId: activeConversationId, agentCode: activeAgentCode });

    if (disabledAfterSended) {
      setDisabled(true);
    }
  };

  useEffect(() => {
    if (isFreshMessage) {
      setDisabled(false);
    } else {
      setDisabled(true);
    }
  }, [isFreshMessage]);

  return (
    <div className="ag:pt-2">
      <Radio.Group
        options={options}
        onChange={(e) => onChange?.(e.target.value)}
        value={value}
        disabled={disabled}
        style={{
          display: "flex",
          flexDirection: "column",
          gap: 8,
        }}
      />
    </div>
  );
};

export default RadioGroupInteractiveWidget;
