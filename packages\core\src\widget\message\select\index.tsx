import { Select } from "antd";
import React, { useContext, useEffect, useState } from "react";

import { useCommandRunner } from "@/command";
import { useActiveAgentCode, useActiveConversationId } from "@/core";
import { MessageRenderContext } from "@/message";
import { BuildInCommand } from "@/types";

interface SelectInteractiveWidgetProps {
  options: { label: string; value: string }[];
  placeholder?: string;
  defaultValue?: string;
  disabledAfterSended?: boolean;
}

const SelectInteractiveWidget: React.FC<SelectInteractiveWidgetProps> = (props) => {
  const { options, placeholder, defaultValue, disabledAfterSended = true } = props;
  const [value, setValue] = useState<string | undefined>(defaultValue);
  const runner = useCommandRunner();
  const [activeConversationId] = useActiveConversationId();
  const [activeAgentCode] = useActiveAgentCode();
  const [disabled, setDisabled] = useState(false);
  const messageRenderContext = useContext(MessageRenderContext);

  const isFreshMessage = messageRenderContext?.message.isFresh;

  const onChange = (selectedValue: string) => {
    setValue(selectedValue);
    const message = `${selectedValue}`;
    runner(BuildInCommand.SendMessage, { message, conversationId: activeConversationId, agentCode: activeAgentCode });

    if (disabledAfterSended) {
      setDisabled(true);
    }
  };

  useEffect(() => {
    if (isFreshMessage) {
      setDisabled(false);
    } else {
      setDisabled(true);
    }
  }, [isFreshMessage]);

  return (
    <div className="ag:pt-2">
      <Select
        options={options}
        onChange={onChange}
        value={value}
        disabled={disabled}
        placeholder={placeholder}
        style={{
          width: "100%",
          minWidth: 200,
        }}
      />
    </div>
  );
};

export default SelectInteractiveWidget;
